# RBAC权限管理系统

本项目实现了一个基于JWT的RBAC（Role-Based Access Control）权限管理系统。

## 功能特性

- 用户管理 (CRUD操作)
- 角色管理 (CRUD操作)
- 权限管理 (CRUD操作)
- 用户角色分配
- 角色权限分配
- 统一API响应格式
- 全局异常处理
- Swagger UI API文档
- JWT认证与授权
- 基于角色的访问控制

## 技术栈

- Spring Boot 3.5.4
- Spring Security
- JWT (JSON Web Token)
- MyBatis
- MySQL
- Swagger UI (springdoc-openapi)
- Lombok
- Apache Commons Lang3

## 数据库结构

数据库结构定义在 `src/main/resources/schema.sql` 文件中，测试数据在 `src/main/resources/data.sql` 文件中。

## API文档

启动应用后，可以通过以下地址访问Swagger UI：

- http://localhost:8080/swagger-ui.html

OpenAPI规范可以通过以下地址访问：

- http://localhost:8080/api-docs

## 使用说明

1. 克隆代码库
2. 在 `src/main/resources/application.yml` 中配置MySQL数据库
3. 运行应用: `mvn spring-boot:run`
4. 访问应用: http://localhost:8080
5. 访问Swagger UI: http://localhost:8080/swagger-ui.html

## RBAC功能说明

### 认证接口

- **用户注册**
  - URL: `POST /api/auth/register`
  - 参数: username, password
  
- **用户登录**
  - URL: `POST /api/auth/login`
  - 参数: username, password
  - 返回: JWT Token

### 受保护接口

- **管理员接口** (需要ADMIN角色)
  - `GET /api/admin/dashboard` - 管理员仪表板
  - `GET /api/admin/users` - 管理用户

- **用户接口** (需要USER或ADMIN角色)
  - `GET /api/user/profile` - 用户资料
  - `GET /api/user/settings` - 用户设置

- **通用用户接口** (需要认证)
  - `GET /api/users/profile` - 用户资料
  - `GET /api/users/{id}` - 根据ID获取用户
  - `GET /api/users?username={username}` - 根据用户名获取用户
  - `GET /api/users` - 获取所有用户
  - `POST /api/users` - 创建用户
  - `PUT /api/users` - 更新用户
  - `DELETE /api/users/{id}` - 删除用户

### 权限控制

权限控制通过以下方式实现：

1. **JWT Token验证**: 每个请求都需要携带有效的JWT Token
2. **角色检查**: 通过Spring Security配置类中的`hasRole()`方法检查用户角色
3. **权限检查**: 通过自定义的`PermissionService`检查用户权限

### 安全配置

安全配置在`SecurityConfig`类中定义：

- `/api/auth/**` - 允许所有访问（登录、注册）
- `/api-docs/**`, `/swagger-ui/**` - 允许所有访问（API文档）
- `/api/admin/**` - 需要ADMIN角色
- `/api/user/**` - 需要USER或ADMIN角色
- 其他所有请求都需要认证