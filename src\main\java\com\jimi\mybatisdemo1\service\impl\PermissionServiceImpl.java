package com.jimi.mybatisdemo1.service.impl;

import com.jimi.mybatisdemo1.entity.Permission;
import com.jimi.mybatisdemo1.mapper.PermissionMapper;
import com.jimi.mybatisdemo1.service.PermissionService;
import com.jimi.mybatisdemo1.common.ResourceNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class PermissionServiceImpl implements PermissionService {
    
    @Autowired
    private PermissionMapper permissionMapper;
    
    @Override
    public Permission findById(Long id) {
        Permission permission = permissionMapper.findById(id);
        if (permission == null) {
            throw new ResourceNotFoundException("Permission not found with id: " + id);
        }
        return permission;
    }
    
    @Override
    public Permission findByName(String name) {
        Permission permission = permissionMapper.findByName(name);
        if (permission == null) {
            throw new ResourceNotFoundException("Permission not found with name: " + name);
        }
        return permission;
    }
    
    @Override
    public List<Permission> findAll() {
        return permissionMapper.findAll();
    }
    
    @Override
    public boolean save(Permission permission) {
        permission.setCreateTime(new Date());
        permission.setUpdateTime(new Date());
        return permissionMapper.insert(permission) > 0;
    }
    
    @Override
    public boolean update(Permission permission) {
        // Check if permission exists before updating
        Permission existingPermission = permissionMapper.findById(permission.getId());
        if (existingPermission == null) {
            throw new ResourceNotFoundException("Permission not found with id: " + permission.getId());
        }
        permission.setUpdateTime(new Date());
        return permissionMapper.update(permission) > 0;
    }
    
    @Override
    public boolean deleteById(Long id) {
        // Check if permission exists before deleting
        Permission existingPermission = permissionMapper.findById(id);
        if (existingPermission == null) {
            throw new ResourceNotFoundException("Permission not found with id: " + id);
        }
        return permissionMapper.deleteById(id) > 0;
    }
    
    @Override
    public boolean hasPermission(Long userId, String permission) {
        // Implementation would check user's permissions
        // This is a placeholder implementation
        return false;
    }
    
    @Override
    public boolean hasRole(Long userId, String roleName) {
        // Implementation would check user's roles
        // This is a placeholder implementation
        return false;
    }
}