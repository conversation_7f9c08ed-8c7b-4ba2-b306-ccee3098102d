-- Clear existing data first
DELETE FROM user;

-- Insert test data into user table
-- Password for all users is "password"
-- BCrypt hash generated for "password": $2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.
INSERT INTO `user` (`id`, `username`, `password`, `email`, `role`, `create_time`, `update_time`) VALUES
(1, 'admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '<EMAIL>', 'ADMIN', NOW(), NOW()),
(2, 'user', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '<EMAIL>', 'USER', NOW(), NOW()),
(3, 'user2', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2uheWG/igi.', '<EMAIL>', 'USER', NOW(), NOW());

-- Insert test data into role table
INSERT INTO `role` (`id`, `name`, `description`, `create_time`, `update_time`) VALUES
(1, 'ADMIN', 'Administrator role with full permissions', NOW(), NOW()),
(2, 'USER', 'Regular user role with limited permissions', NOW(), NOW()),
(3, 'GUEST', 'Guest role with read-only permissions', NOW(), NOW());

-- Insert test data into permission table
INSERT INTO `permission` (`id`, `name`, `description`, `url`, `method`, `create_time`, `update_time`) VALUES
(1, 'USER_CREATE', 'Create user permission', '/api/users', 'POST', NOW(), NOW()),
(2, 'USER_READ', 'Read user permission', '/api/users', 'GET', NOW(), NOW()),
(3, 'USER_UPDATE', 'Update user permission', '/api/users', 'PUT', NOW(), NOW()),
(4, 'USER_DELETE', 'Delete user permission', '/api/users', 'DELETE', NOW(), NOW()),
(5, 'ROLE_ASSIGN', 'Assign role to user permission', '/api/user-role', 'POST', NOW(), NOW()),
(6, 'ROLE_REVOKE', 'Revoke role from user permission', '/api/user-role', 'DELETE', NOW(), NOW());

-- Insert test data into user_role table
INSERT INTO `user_role` (`id`, `user_id`, `role_id`, `create_time`) VALUES
(1, 1, 1, NOW()), -- admin user has ADMIN role
(2, 2, 2, NOW()), -- user1 has USER role
(3, 3, 3, NOW()); -- user2 has GUEST role

-- Insert test data into role_permission table
INSERT INTO `role_permission` (`id`, `role_id`, `permission_id`, `create_time`) VALUES
(1, 1, 1, NOW()), -- ADMIN role has USER_CREATE permission
(2, 1, 2, NOW()), -- ADMIN role has USER_READ permission
(3, 1, 3, NOW()), -- ADMIN role has USER_UPDATE permission
(4, 1, 4, NOW()), -- ADMIN role has USER_DELETE permission
(5, 1, 5, NOW()), -- ADMIN role has ROLE_ASSIGN permission
(6, 1, 6, NOW()), -- ADMIN role has ROLE_REVOKE permission
(7, 2, 2, NOW()), -- USER role has USER_READ permission
(8, 3, 2, NOW()); -- GUEST role has USER_READ permission