package com.jimi.mybatisdemo1.config;

import com.jimi.mybatisdemo1.entity.User;
import com.jimi.mybatisdemo1.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 数据初始化器
 * 在应用启动时创建测试用户（如果不存在）
 */
@Component
public class DataInitializer implements CommandLineRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(DataInitializer.class);
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @Override
    public void run(String... args) throws Exception {
        logger.info("Initializing test data...");
        
        // 创建管理员用户
        createUserIfNotExists("admin", "password", "<EMAIL>", "ADMIN");
        
        // 创建普通用户
        createUserIfNotExists("user", "password", "<EMAIL>", "USER");
        
        logger.info("Test data initialization completed.");
    }
    
    private void createUserIfNotExists(String username, String password, String email, String role) {
        try {
            User existingUser = userService.findByUsername(username);
            if (existingUser == null) {
                User user = new User();
                user.setUsername(username);
                user.setPassword(passwordEncoder.encode(password));
                user.setEmail(email);
                user.setRole(role);
                user.setCreateTime(new Date());
                user.setUpdateTime(new Date());

                boolean result = userService.save(user);
                if (result) {
                    logger.info("Created test user: {} with role: {}", username, role);
                } else {
                    logger.warn("Failed to create test user: {}", username);
                }
            } else {
                // 更新现有用户的密码以确保正确的哈希
                String encodedPassword = passwordEncoder.encode(password);
                logger.info("Test user already exists: {}, updating password hash", username);
                logger.debug("Old password hash: {}", existingUser.getPassword());
                logger.debug("New password hash: {}", encodedPassword);

                existingUser.setPassword(encodedPassword);
                existingUser.setUpdateTime(new Date());

                boolean result = userService.update(existingUser);
                if (result) {
                    logger.info("Updated password for test user: {}", username);
                } else {
                    logger.warn("Failed to update password for test user: {}", username);
                }
            }
        } catch (Exception e) {
            logger.error("Error creating test user {}: {}", username, e.getMessage());
        }
    }
}
