package com.jimi.mybatisdemo1.service.impl;

import com.jimi.mybatisdemo1.entity.UserRole;
import com.jimi.mybatisdemo1.mapper.UserRoleMapper;
import com.jimi.mybatisdemo1.service.UserRoleService;
import com.jimi.mybatisdemo1.common.ResourceNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class UserRoleServiceImpl implements UserRoleService {
    
    @Autowired
    private UserRoleMapper userRoleMapper;
    
    @Override
    public UserRole findById(Long id) {
        UserRole userRole = userRoleMapper.findById(id);
        if (userRole == null) {
            throw new ResourceNotFoundException("UserRole not found with id: " + id);
        }
        return userRole;
    }
    
    @Override
    public List<UserRole> findByUserId(Long userId) {
        return userRoleMapper.findByUserId(userId);
    }
    
    @Override
    public List<UserRole> findByRoleId(Long roleId) {
        return userRoleMapper.findByRoleId(roleId);
    }
    
    @Override
    public List<UserRole> findAll() {
        return userRoleMapper.findAll();
    }
    
    @Override
    public boolean save(UserRole userRole) {
        userRole.setCreateTime(new Date());
        return userRoleMapper.insert(userRole) > 0;
    }
    
    @Override
    public boolean update(UserRole userRole) {
        // Check if userRole exists before updating
        UserRole existingUserRole = userRoleMapper.findById(userRole.getId());
        if (existingUserRole == null) {
            throw new ResourceNotFoundException("UserRole not found with id: " + userRole.getId());
        }
        userRole.setCreateTime(new Date());
        return userRoleMapper.update(userRole) > 0;
    }
    
    @Override
    public boolean deleteById(Long id) {
        // Check if userRole exists before deleting
        UserRole existingUserRole = userRoleMapper.findById(id);
        if (existingUserRole == null) {
            throw new ResourceNotFoundException("UserRole not found with id: " + id);
        }
        return userRoleMapper.deleteById(id) > 0;
    }
    
    @Override
    public boolean deleteByUserId(Long userId) {
        // Check if any userRole exists for this user before deleting
        List<UserRole> userRoles = userRoleMapper.findByUserId(userId);
        if (userRoles.isEmpty()) {
            throw new ResourceNotFoundException("UserRole not found for userId: " + userId);
        }
        return userRoleMapper.deleteByUserId(userId) > 0;
    }
    
    @Override
    public boolean deleteByRoleId(Long roleId) {
        // Check if any userRole exists for this role before deleting
        List<UserRole> userRoles = userRoleMapper.findByRoleId(roleId);
        if (userRoles.isEmpty()) {
            throw new ResourceNotFoundException("UserRole not found for roleId: " + roleId);
        }
        return userRoleMapper.deleteByRoleId(roleId) > 0;
    }
    
    @Override
    public boolean deleteByUserIdAndRoleId(Long userId, Long roleId) {
        // Check if the specific user-role association exists before deleting
        int count = userRoleMapper.deleteByUserIdAndRoleId(userId, roleId);
        if (count == 0) {
            throw new ResourceNotFoundException("UserRole association not found for userId: " + userId + " and roleId: " + roleId);
        }
        return count > 0;
    }
}