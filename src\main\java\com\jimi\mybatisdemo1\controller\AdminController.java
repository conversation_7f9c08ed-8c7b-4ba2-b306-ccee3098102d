package com.jimi.mybatisdemo1.controller;

import com.jimi.mybatisdemo1.common.ApiResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/admin")
public class AdminController {
    
    @GetMapping("/dashboard")
    public ApiResponse<String> adminDashboard() {
        return ApiResponse.success("Welcome to Admin Dashboard");
    }
    
    @GetMapping("/users")
    public ApiResponse<String> manageUsers() {
        return ApiResponse.success("Managing users");
    }
}