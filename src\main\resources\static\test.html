<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RBAC Demo - API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .response {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .token-display {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <h1>RBAC Demo - API测试页面</h1>
    
    <div class="info">
        <h3>使用说明：</h3>
        <ol>
            <li>首先使用登录接口获取JWT token</li>
            <li>复制获取到的token</li>
            <li>访问 <a href="/swagger-ui.html" target="_blank">Swagger UI</a></li>
            <li>点击右上角的 "Authorize" 按钮</li>
            <li>输入: <code>Bearer &lt;your-token&gt;</code></li>
            <li>现在可以测试需要认证的API了</li>
        </ol>
    </div>

    <div class="container">
        <h2>1. 用户登录</h2>
        <div class="form-group">
            <label for="loginUsername">用户名:</label>
            <input type="text" id="loginUsername" value="admin">
        </div>
        <div class="form-group">
            <label for="loginPassword">密码:</label>
            <input type="password" id="loginPassword" value="password">
        </div>
        <button onclick="login()">登录</button>
        <div id="loginResponse" class="response"></div>
        <div id="tokenDisplay" class="token-display" style="display: none;">
            <strong>JWT Token:</strong>
            <div id="tokenValue"></div>
        </div>
    </div>

    <div class="container">
        <h2>2. 测试认证接口</h2>
        <button onclick="testUserProfile()">测试用户资料接口 (需要认证)</button>
        <button onclick="testAdminDashboard()">测试管理员接口 (需要ADMIN角色)</button>
        <div id="testResponse" class="response"></div>
    </div>

    <div class="container">
        <h2>3. 测试异常处理</h2>
        <button onclick="testUnauthorized()">测试未认证访问</button>
        <button onclick="testInvalidToken()">测试无效Token</button>
        <button onclick="testNotFound()">测试404错误</button>
        <div id="errorResponse" class="response"></div>
    </div>

    <script>
        let currentToken = '';

        async function login() {
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                const responseDiv = document.getElementById('loginResponse');
                
                if (response.ok && data.code === 200) {
                    responseDiv.className = 'response success';
                    responseDiv.textContent = JSON.stringify(data, null, 2);
                    currentToken = data.data.token;
                    
                    // 显示token
                    document.getElementById('tokenValue').textContent = currentToken;
                    document.getElementById('tokenDisplay').style.display = 'block';
                } else {
                    responseDiv.className = 'response error';
                    responseDiv.textContent = JSON.stringify(data, null, 2);
                }
            } catch (error) {
                document.getElementById('loginResponse').className = 'response error';
                document.getElementById('loginResponse').textContent = 'Error: ' + error.message;
            }
        }

        async function testUserProfile() {
            try {
                const response = await fetch('/api/users/profile', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + currentToken
                    }
                });
                
                const data = await response.json();
                const responseDiv = document.getElementById('testResponse');
                
                if (response.ok) {
                    responseDiv.className = 'response success';
                } else {
                    responseDiv.className = 'response error';
                }
                responseDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('testResponse').className = 'response error';
                document.getElementById('testResponse').textContent = 'Error: ' + error.message;
            }
        }

        async function testAdminDashboard() {
            try {
                const response = await fetch('/api/admin/dashboard', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + currentToken
                    }
                });
                
                const data = await response.json();
                const responseDiv = document.getElementById('testResponse');
                
                if (response.ok) {
                    responseDiv.className = 'response success';
                } else {
                    responseDiv.className = 'response error';
                }
                responseDiv.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('testResponse').className = 'response error';
                document.getElementById('testResponse').textContent = 'Error: ' + error.message;
            }
        }

        async function testUnauthorized() {
            try {
                const response = await fetch('/api/users/profile', {
                    method: 'GET'
                    // 不发送Authorization header
                });
                
                const data = await response.json();
                document.getElementById('errorResponse').className = 'response error';
                document.getElementById('errorResponse').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('errorResponse').className = 'response error';
                document.getElementById('errorResponse').textContent = 'Error: ' + error.message;
            }
        }

        async function testInvalidToken() {
            try {
                const response = await fetch('/api/users/profile', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer invalid-token'
                    }
                });
                
                const data = await response.json();
                document.getElementById('errorResponse').className = 'response error';
                document.getElementById('errorResponse').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('errorResponse').className = 'response error';
                document.getElementById('errorResponse').textContent = 'Error: ' + error.message;
            }
        }

        async function testNotFound() {
            try {
                const response = await fetch('/api/nonexistent', {
                    method: 'GET',
                    headers: {
                        'Authorization': 'Bearer ' + currentToken
                    }
                });
                
                const data = await response.json();
                document.getElementById('errorResponse').className = 'response error';
                document.getElementById('errorResponse').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('errorResponse').className = 'response error';
                document.getElementById('errorResponse').textContent = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
