package com.jimi.mybatisdemo1.controller;

import com.jimi.mybatisdemo1.entity.User;
import com.jimi.mybatisdemo1.entity.UserRole;
import com.jimi.mybatisdemo1.service.UserService;
import com.jimi.mybatisdemo1.service.UserRoleService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/user-management")
@Tag(name = "User Management", description = "Operations pertaining to users in the system")
public class UserManagementController {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private UserRoleService userRoleService;
    
    @GetMapping("/users/{id}")
    @Operation(summary = "Find user by ID", description = "Get a user by its unique identifier")
    @Parameters({
        @Parameter(name = "id", in = ParameterIn.PATH, description = "ID of user to be searched", required = true, schema = @Schema(type = "integer", format = "int64"))
    })
    @ApiResponse(responseCode = "200", description = "User found", content = @Content(schema = @Schema(implementation = User.class)))
    @ApiResponse(responseCode = "404", description = "User not found")
    public com.jimi.mybatisdemo1.common.ApiResponse<User> findUserById(@PathVariable Long id) {
        User user = userService.findById(id);
        if (user != null) {
            return com.jimi.mybatisdemo1.common.ApiResponse.success(user);
        } else {
            return com.jimi.mybatisdemo1.common.ApiResponse.error(404, "User not found");
        }
    }
    
    @GetMapping("/users")
    @Operation(summary = "Get all users", description = "Returns a list of all users in the system")
    @ApiResponse(responseCode = "200", description = "List of users", content = @Content(schema = @Schema(implementation = User.class)))
    public com.jimi.mybatisdemo1.common.ApiResponse<List<User>> findAllUsers() {
        List<User> users = userService.findAll();
        return com.jimi.mybatisdemo1.common.ApiResponse.success(users);
    }
    
    @PostMapping("/users")
    @Operation(summary = "Save a new user", description = "Create a new user in the system")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "User object to be created", required = true, content = @Content(schema = @Schema(implementation = User.class)))
    @ApiResponse(responseCode = "200", description = "User saved successfully")
    @ApiResponse(responseCode = "400", description = "Failed to save user")
    public com.jimi.mybatisdemo1.common.ApiResponse<String> saveUser(@RequestBody User user) {
        boolean result = userService.save(user);
        if (result) {
            return com.jimi.mybatisdemo1.common.ApiResponse.success("User saved successfully");
        } else {
            return com.jimi.mybatisdemo1.common.ApiResponse.error("Failed to save user");
        }
    }
    
    @PutMapping("/users")
    @Operation(summary = "Update a user", description = "Update an existing user in the system")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "User object to be updated", required = true, content = @Content(schema = @Schema(implementation = User.class)))
    @ApiResponse(responseCode = "200", description = "User updated successfully")
    @ApiResponse(responseCode = "400", description = "Failed to update user")
    public com.jimi.mybatisdemo1.common.ApiResponse<String> updateUser(@RequestBody User user) {
        boolean result = userService.update(user);
        if (result) {
            return com.jimi.mybatisdemo1.common.ApiResponse.success("User updated successfully");
        } else {
            return com.jimi.mybatisdemo1.common.ApiResponse.error("Failed to update user");
        }
    }
    
    @DeleteMapping("/users/{id}")
    @Operation(summary = "Delete a user", description = "Delete an existing user by its ID")
    @Parameters({
        @Parameter(name = "id", in = ParameterIn.PATH, description = "ID of user to be deleted", required = true, schema = @Schema(type = "integer", format = "int64"))
    })
    @ApiResponse(responseCode = "200", description = "User deleted successfully")
    @ApiResponse(responseCode = "404", description = "Failed to delete user")
    public com.jimi.mybatisdemo1.common.ApiResponse<String> deleteUser(@PathVariable Long id) {
        boolean result = userService.deleteById(id);
        if (result) {
            return com.jimi.mybatisdemo1.common.ApiResponse.success("User deleted successfully");
        } else {
            return com.jimi.mybatisdemo1.common.ApiResponse.error("Failed to delete user");
        }
    }
    
    @PostMapping("/users/{userId}/roles/{roleId}")
    @Operation(summary = "Assign a role to a user", description = "Assign a role to an existing user")
    @Parameters({
        @Parameter(name = "userId", in = ParameterIn.PATH, description = "ID of user to assign role to", required = true, schema = @Schema(type = "integer", format = "int64")),
        @Parameter(name = "roleId", in = ParameterIn.PATH, description = "ID of role to assign", required = true, schema = @Schema(type = "integer", format = "int64"))
    })
    @ApiResponse(responseCode = "200", description = "Role assigned to user successfully")
    @ApiResponse(responseCode = "400", description = "Failed to assign role to user")
    public com.jimi.mybatisdemo1.common.ApiResponse<String> assignRoleToUser(@PathVariable Long userId, @PathVariable Long roleId) {
        UserRole userRole = new UserRole();
        userRole.setUserId(userId);
        userRole.setRoleId(roleId);
        boolean result = userRoleService.save(userRole);
        if (result) {
            return com.jimi.mybatisdemo1.common.ApiResponse.success("Role assigned to user successfully");
        } else {
            return com.jimi.mybatisdemo1.common.ApiResponse.error("Failed to assign role to user");
        }
    }
    
    @DeleteMapping("/user-role/{userId}/{roleId}")
    @Operation(summary = "Delete a user-role association", description = "Remove a role from a user")
    @Parameters({
        @Parameter(name = "userId", in = ParameterIn.PATH, description = "ID of user to remove role from", required = true, schema = @Schema(type = "integer", format = "int64")),
        @Parameter(name = "roleId", in = ParameterIn.PATH, description = "ID of role to remove", required = true, schema = @Schema(type = "integer", format = "int64"))
    })
    @ApiResponse(responseCode = "200", description = "User-role association deleted successfully")
    @ApiResponse(responseCode = "404", description = "User-role association not found")
    public com.jimi.mybatisdemo1.common.ApiResponse<String> deleteUserRole(@PathVariable Long userId, @PathVariable Long roleId) {
        boolean result = userRoleService.deleteByUserIdAndRoleId(userId, roleId);
        if (result) {
            return com.jimi.mybatisdemo1.common.ApiResponse.success("User-role association deleted successfully");
        } else {
            return com.jimi.mybatisdemo1.common.ApiResponse.error(404, "User-role association not found");
        }
    }
}