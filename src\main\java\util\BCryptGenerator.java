import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

public class BCryptGenerator {
    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        String rawPassword = "yourplaintextpassword"; // Replace with your desired plaintext password
        String encodedPassword = encoder.encode(rawPassword);
        System.out.println("Plaintext Password: " + rawPassword);
        System.out.println("Encoded Password: " + encodedPassword);
    }
}