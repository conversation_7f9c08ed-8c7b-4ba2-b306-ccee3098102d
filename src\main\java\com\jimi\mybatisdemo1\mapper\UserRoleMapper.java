package com.jimi.mybatisdemo1.mapper;

import com.jimi.mybatisdemo1.entity.UserRole;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface UserRoleMapper {
    
    @Select("SELECT * FROM user_role WHERE id = #{id}")
    UserRole findById(Long id);
    
    @Select("SELECT * FROM user_role WHERE user_id = #{userId}")
    List<UserRole> findByUserId(Long userId);
    
    @Select("SELECT * FROM user_role WHERE role_id = #{roleId}")
    List<UserRole> findByRoleId(Long roleId);
    
    @Select("SELECT * FROM user_role")
    List<UserRole> findAll();
    
    @Insert("INSERT INTO user_role(user_id, role_id, create_time) VALUES(#{userId}, #{roleId}, #{createTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(UserRole userRole);
    
    @Update("UPDATE user_role SET user_id=#{userId}, role_id=#{roleId}, create_time=#{createTime} WHERE id=#{id}")
    int update(UserRole userRole);
    
    @Delete("DELETE FROM user_role WHERE id = #{id}")
    int deleteById(Long id);
    
    @Delete("DELETE FROM user_role WHERE user_id = #{userId}")
    int deleteByUserId(Long userId);
    
    @Delete("DELETE FROM user_role WHERE role_id = #{roleId}")
    int deleteByRoleId(Long roleId);
    
    @Delete("DELETE FROM user_role WHERE user_id = #{userId} AND role_id = #{roleId}")
    int deleteByUserIdAndRoleId(@Param("userId") Long userId, @Param("roleId") Long roleId);
}