package com.jimi.mybatisdemo1.service.impl;

import com.jimi.mybatisdemo1.entity.Role;
import com.jimi.mybatisdemo1.mapper.RoleMapper;
import com.jimi.mybatisdemo1.service.RoleService;
import com.jimi.mybatisdemo1.common.ResourceNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class RoleServiceImpl implements RoleService {
    
    @Autowired
    private RoleMapper roleMapper;
    
    @Override
    public Role findById(Long id) {
        Role role = roleMapper.findById(id);
        if (role == null) {
            throw new ResourceNotFoundException("Role not found with id: " + id);
        }
        return role;
    }
    
    @Override
    public Role findByName(String name) {
        Role role = roleMapper.findByName(name);
        if (role == null) {
            throw new ResourceNotFoundException("Role not found with name: " + name);
        }
        return role;
    }
    
    @Override
    public List<Role> findAll() {
        return roleMapper.findAll();
    }
    
    @Override
    public boolean save(Role role) {
        role.setCreateTime(new Date());
        role.setUpdateTime(new Date());
        return roleMapper.insert(role) > 0;
    }
    
    @Override
    public boolean update(Role role) {
        // Check if role exists before updating
        Role existingRole = roleMapper.findById(role.getId());
        if (existingRole == null) {
            throw new ResourceNotFoundException("Role not found with id: " + role.getId());
        }
        role.setUpdateTime(new Date());
        return roleMapper.update(role) > 0;
    }
    
    @Override
    public boolean deleteById(Long id) {
        // Check if role exists before deleting
        Role existingRole = roleMapper.findById(id);
        if (existingRole == null) {
            throw new ResourceNotFoundException("Role not found with id: " + id);
        }
        return roleMapper.deleteById(id) > 0;
    }
}