package com.jimi.mybatisdemo1;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * 密码哈希生成器 - 用于生成测试数据的密码哈希
 */
public class PasswordHashGenerator {
    public static void main(String[] args) {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        
        String password = "password";
        String hash = encoder.encode(password);
        
        System.out.println("Password: " + password);
        System.out.println("BCrypt Hash: " + hash);
        System.out.println();
        
        // 验证哈希
        boolean matches = encoder.matches(password, hash);
        System.out.println("Hash verification: " + matches);
    }
}
