package com.jimi.mybatisdemo1;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

@SpringBootTest
class Mybatisdemo1ApplicationTests {

    @Test
    void contextLoads() {
        BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
        String rawPassword = "123456"; // Replace with your desired plaintext password
        String encodedPassword = encoder.encode(rawPassword);
        System.out.println("Plaintext Password: " + rawPassword);
        System.out.println("Encoded Password: " + encodedPassword);
    }

}
