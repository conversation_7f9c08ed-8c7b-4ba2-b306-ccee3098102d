package com.jimi.mybatisdemo1.mapper;

import com.jimi.mybatisdemo1.entity.RolePermission;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface RolePermissionMapper {
    
    @Select("SELECT * FROM role_permission WHERE id = #{id}")
    RolePermission findById(Long id);
    
    @Select("SELECT * FROM role_permission WHERE role_id = #{roleId}")
    List<RolePermission> findByRoleId(Long roleId);
    
    @Select("SELECT * FROM role_permission WHERE permission_id = #{permissionId}")
    List<RolePermission> findByPermissionId(Long permissionId);
    
    @Select("SELECT * FROM role_permission")
    List<RolePermission> findAll();
    
    @Insert("INSERT INTO role_permission(role_id, permission_id, create_time) VALUES(#{roleId}, #{permissionId}, #{createTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(RolePermission rolePermission);
    
    @Update("UPDATE role_permission SET role_id=#{roleId}, permission_id=#{permissionId}, create_time=#{createTime} WHERE id=#{id}")
    int update(RolePermission rolePermission);
    
    @Delete("DELETE FROM role_permission WHERE id = #{id}")
    int deleteById(Long id);
    
    @Delete("DELETE FROM role_permission WHERE role_id = #{roleId}")
    int deleteByRoleId(Long roleId);
    
    @Delete("DELETE FROM role_permission WHERE permission_id = #{permissionId}")
    int deleteByPermissionId(Long permissionId);
    
    @Delete("DELETE FROM role_permission WHERE role_id = #{roleId} AND permission_id = #{permissionId}")
    int deleteByRoleIdAndPermissionId(@Param("roleId") Long roleId, @Param("permissionId") Long permissionId);
}