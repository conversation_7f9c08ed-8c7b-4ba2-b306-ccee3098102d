package com.jimi.mybatisdemo1.controller;

import com.jimi.mybatisdemo1.entity.Permission;
import com.jimi.mybatisdemo1.service.PermissionService;
import com.jimi.mybatisdemo1.common.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/permissions")
public class PermissionController {
    
    @Autowired
    private PermissionService permissionService;
    
    @GetMapping("/{id}")
    public ApiResponse<Permission> findById(@PathVariable Long id) {
        Permission permission = permissionService.findById(id);
        if (permission != null) {
            return ApiResponse.success(permission);
        } else {
            return ApiResponse.error(404, "Permission not found");
        }
    }
    
    @GetMapping(params = "name")
    public ApiResponse<Permission> findByName(@RequestParam String name) {
        Permission permission = permissionService.findByName(name);
        if (permission != null) {
            return ApiResponse.success(permission);
        } else {
            return ApiResponse.error(404, "Permission not found");
        }
    }
    
    @GetMapping
    public ApiResponse<List<Permission>> findAll() {
        List<Permission> permissions = permissionService.findAll();
        return ApiResponse.success(permissions);
    }
    
    @PostMapping
    public ApiResponse<String> save(@RequestBody Permission permission) {
        boolean result = permissionService.save(permission);
        if (result) {
            return ApiResponse.success("Permission saved successfully");
        } else {
            return ApiResponse.error("Failed to save permission");
        }
    }
    
    @PutMapping
    public ApiResponse<String> update(@RequestBody Permission permission) {
        boolean result = permissionService.update(permission);
        if (result) {
            return ApiResponse.success("Permission updated successfully");
        } else {
            return ApiResponse.error("Failed to update permission");
        }
    }
    
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteById(@PathVariable Long id) {
        boolean result = permissionService.deleteById(id);
        if (result) {
            return ApiResponse.success("Permission deleted successfully");
        } else {
            return ApiResponse.error("Failed to delete permission");
        }
    }
}