package com.jimi.mybatisdemo1.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("RBAC Demo API")
                        .version("1.0")
                        .description("RBAC (Role-Based Access Control) Demo Project API Documentation")
                        .license(new License().name("Apache 2.0")
                                .url("http://springdoc.org")));
    }
}