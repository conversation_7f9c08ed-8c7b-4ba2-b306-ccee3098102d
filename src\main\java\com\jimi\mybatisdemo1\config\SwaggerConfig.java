package com.jimi.mybatisdemo1.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.Components;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerConfig {

    private static final String SECURITY_SCHEME_NAME = "Bearer Authentication";

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("RBAC Demo API")
                        .version("1.0")
                        .description("RBAC (Role-Based Access Control) Demo Project API Documentation\n\n" +
                                "## 如何使用JWT认证:\n" +
                                "1. 首先调用 `/api/auth/login` 接口获取JWT token\n" +
                                "2. 点击右上角的 'Authorize' 按钮\n" +
                                "3. 在弹出的对话框中输入: `Bearer <your-jwt-token>`\n" +
                                "4. 点击 'Authorize' 按钮完成认证\n" +
                                "5. 现在可以调用需要认证的API了")
                        .license(new License().name("Apache 2.0")
                                .url("http://springdoc.org")))
                .addSecurityItem(new SecurityRequirement().addList(SECURITY_SCHEME_NAME))
                .components(new Components()
                        .addSecuritySchemes(SECURITY_SCHEME_NAME,
                                new SecurityScheme()
                                        .name(SECURITY_SCHEME_NAME)
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")
                                        .description("请输入JWT token，格式: Bearer <token>")));
    }
}