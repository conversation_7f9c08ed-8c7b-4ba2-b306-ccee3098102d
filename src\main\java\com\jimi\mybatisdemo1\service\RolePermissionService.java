package com.jimi.mybatisdemo1.service;

import com.jimi.mybatisdemo1.entity.RolePermission;

import java.util.List;

public interface RolePermissionService {
    RolePermission findById(Long id);
    List<RolePermission> findByRoleId(Long roleId);
    List<RolePermission> findByPermissionId(Long permissionId);
    List<RolePermission> findAll();
    boolean save(RolePermission rolePermission);
    boolean update(RolePermission rolePermission);
    boolean deleteById(Long id);
    boolean deleteByRoleId(Long roleId);
    boolean deleteByPermissionId(Long permissionId);
    boolean deleteByRoleIdAndPermissionId(Long roleId, Long permissionId);
}