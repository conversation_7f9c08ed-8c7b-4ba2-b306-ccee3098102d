package com.jimi.mybatisdemo1.service;

import com.jimi.mybatisdemo1.entity.UserRole;

import java.util.List;

public interface UserRoleService {
    UserRole findById(Long id);
    List<UserRole> findByUserId(Long userId);
    List<UserRole> findByRoleId(Long roleId);
    List<UserRole> findAll();
    boolean save(UserRole userRole);
    boolean update(UserRole userRole);
    boolean deleteById(Long id);
    boolean deleteByUserId(Long userId);
    boolean deleteByRoleId(Long roleId);
    boolean deleteByUserIdAndRoleId(Long userId, Long roleId);
}