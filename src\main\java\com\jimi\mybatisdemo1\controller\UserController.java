package com.jimi.mybatisdemo1.controller;

import com.jimi.mybatisdemo1.entity.User;
import com.jimi.mybatisdemo1.service.UserService;
import com.jimi.mybatisdemo1.common.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

@RestController
@RequestMapping("/api/users")
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;

    @GetMapping("/{id}")
    public ApiResponse<User> findById(@PathVariable Long id) {
        logger.debug("Received request to find user by id: {}", id);
        User user = userService.findById(id);
        if (user != null) {
            logger.debug("Found user: {}", user.getUsername());
            return ApiResponse.success(user);
        } else {
            logger.debug("User not found with id: {}", id);
            return ApiResponse.error(404, "User not found");
        }
    }
    
    @GetMapping(params = "username")
    public ApiResponse<User> findByUsername(@RequestParam String username) {
        logger.debug("Received request to find user by username: {}", username);
        User user = userService.findByUsername(username);
        if (user != null) {
            logger.debug("Found user: {}", user.getUsername());
            return ApiResponse.success(user);
        } else {
            logger.debug("User not found with username: {}", username);
            return ApiResponse.error(404, "User not found");
        }
    }
    
    @GetMapping
    public ApiResponse<List<User>> findAll() {
        logger.debug("Received request to find all users");
        List<User> users = userService.findAll();
        logger.debug("Found {} users", users.size());
        return ApiResponse.success(users);
    }
    
    @PostMapping
    public ApiResponse<String> save(@RequestBody User user) {
        logger.debug("Received request to save user: {}", user.getUsername());
        boolean result = userService.save(user);
        if (result) {
            logger.debug("User saved successfully: {}", user.getUsername());
            return ApiResponse.success("User saved successfully");
        } else {
            logger.debug("Failed to save user: {}", user.getUsername());
            return ApiResponse.error("Failed to save user");
        }
    }
    
    @PutMapping
    public ApiResponse<String> update(@RequestBody User user) {
        logger.debug("Received request to update user: {}", user.getUsername());
        boolean result = userService.update(user);
        if (result) {
            logger.debug("User updated successfully: {}", user.getUsername());
            return ApiResponse.success("User updated successfully");
        } else {
            logger.debug("Failed to update user: {}", user.getUsername());
            return ApiResponse.error("Failed to update user");
        }
    }
    
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteById(@PathVariable Long id) {
        logger.debug("Received request to delete user by id: {}", id);
        boolean result = userService.deleteById(id);
        if (result) {
            logger.debug("User deleted successfully with id: {}", id);
            return ApiResponse.success("User deleted successfully");
        } else {
            logger.debug("Failed to delete user with id: {}", id);
            return ApiResponse.error("Failed to delete user");
        }
    }
    
    @GetMapping("/profile")
    public ApiResponse<String> userProfile() {
        return ApiResponse.success("User profile accessible only with authentication");
    }
}