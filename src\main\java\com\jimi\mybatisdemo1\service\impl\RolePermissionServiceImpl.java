package com.jimi.mybatisdemo1.service.impl;

import com.jimi.mybatisdemo1.entity.RolePermission;
import com.jimi.mybatisdemo1.mapper.RolePermissionMapper;
import com.jimi.mybatisdemo1.service.RolePermissionService;
import com.jimi.mybatisdemo1.common.ResourceNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class RolePermissionServiceImpl implements RolePermissionService {
    
    @Autowired
    private RolePermissionMapper rolePermissionMapper;
    
    @Override
    public RolePermission findById(Long id) {
        RolePermission rolePermission = rolePermissionMapper.findById(id);
        if (rolePermission == null) {
            throw new ResourceNotFoundException("RolePermission not found with id: " + id);
        }
        return rolePermission;
    }
    
    @Override
    public List<RolePermission> findByRoleId(Long roleId) {
        return rolePermissionMapper.findByRoleId(roleId);
    }
    
    @Override
    public List<RolePermission> findByPermissionId(Long permissionId) {
        return rolePermissionMapper.findByPermissionId(permissionId);
    }
    
    @Override
    public List<RolePermission> findAll() {
        return rolePermissionMapper.findAll();
    }
    
    @Override
    public boolean save(RolePermission rolePermission) {
        rolePermission.setCreateTime(new Date());
        return rolePermissionMapper.insert(rolePermission) > 0;
    }
    
    @Override
    public boolean update(RolePermission rolePermission) {
        // Check if rolePermission exists before updating
        RolePermission existingRolePermission = rolePermissionMapper.findById(rolePermission.getId());
        if (existingRolePermission == null) {
            throw new ResourceNotFoundException("RolePermission not found with id: " + rolePermission.getId());
        }
        rolePermission.setCreateTime(new Date());
        return rolePermissionMapper.update(rolePermission) > 0;
    }
    
    @Override
    public boolean deleteById(Long id) {
        // Check if rolePermission exists before deleting
        RolePermission existingRolePermission = rolePermissionMapper.findById(id);
        if (existingRolePermission == null) {
            throw new ResourceNotFoundException("RolePermission not found with id: " + id);
        }
        return rolePermissionMapper.deleteById(id) > 0;
    }
    
    @Override
    public boolean deleteByRoleId(Long roleId) {
        // Check if any rolePermission exists for this role before deleting
        List<RolePermission> rolePermissions = rolePermissionMapper.findByRoleId(roleId);
        if (rolePermissions.isEmpty()) {
            throw new ResourceNotFoundException("RolePermission not found for roleId: " + roleId);
        }
        return rolePermissionMapper.deleteByRoleId(roleId) > 0;
    }
    
    @Override
    public boolean deleteByPermissionId(Long permissionId) {
        // Check if any rolePermission exists for this permission before deleting
        List<RolePermission> rolePermissions = rolePermissionMapper.findByPermissionId(permissionId);
        if (rolePermissions.isEmpty()) {
            throw new ResourceNotFoundException("RolePermission not found for permissionId: " + permissionId);
        }
        return rolePermissionMapper.deleteByPermissionId(permissionId) > 0;
    }
    
    @Override
    public boolean deleteByRoleIdAndPermissionId(Long roleId, Long permissionId) {
        // Check if the specific role-permission association exists before deleting
        int count = rolePermissionMapper.deleteByRoleIdAndPermissionId(roleId, permissionId);
        if (count == 0) {
            throw new ResourceNotFoundException("RolePermission association not found for roleId: " + roleId + " and permissionId: " + permissionId);
        }
        return count > 0;
    }
}