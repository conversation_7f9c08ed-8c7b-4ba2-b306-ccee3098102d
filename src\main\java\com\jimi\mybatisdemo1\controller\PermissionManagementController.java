package com.jimi.mybatisdemo1.controller;

import com.jimi.mybatisdemo1.entity.Permission;
import com.jimi.mybatisdemo1.service.PermissionService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/permission-management")
@Tag(name = "Permission Management", description = "Operations pertaining to permissions in the system")
public class PermissionManagementController {
    
    @Autowired
    private PermissionService permissionService;
    
    @GetMapping("/permissions/{id}")
    @Operation(summary = "Find permission by ID", description = "Get a permission by its unique identifier")
    @Parameters({
        @Parameter(name = "id", in = ParameterIn.PATH, description = "ID of permission to be searched", required = true, schema = @Schema(type = "integer", format = "int64"))
    })
    @ApiResponse(responseCode = "200", description = "Permission found", content = @Content(schema = @Schema(implementation = Permission.class)))
    @ApiResponse(responseCode = "404", description = "Permission not found")
    public com.jimi.mybatisdemo1.common.ApiResponse<Permission> findPermissionById(@PathVariable Long id) {
        Permission permission = permissionService.findById(id);
        if (permission != null) {
            return com.jimi.mybatisdemo1.common.ApiResponse.success(permission);
        } else {
            return com.jimi.mybatisdemo1.common.ApiResponse.error(404, "Permission not found");
        }
    }
    
    @GetMapping("/permissions")
    @Operation(summary = "Get all permissions", description = "Returns a list of all permissions in the system")
    @ApiResponse(responseCode = "200", description = "List of permissions", content = @Content(schema = @Schema(implementation = Permission.class)))
    public com.jimi.mybatisdemo1.common.ApiResponse<List<Permission>> findAllPermissions() {
        List<Permission> permissions = permissionService.findAll();
        return com.jimi.mybatisdemo1.common.ApiResponse.success(permissions);
    }
    
    @PostMapping("/permissions")
    @Operation(summary = "Save a new permission", description = "Create a new permission in the system")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "Permission object to be created", required = true, content = @Content(schema = @Schema(implementation = Permission.class)))
    @ApiResponse(responseCode = "200", description = "Permission saved successfully")
    @ApiResponse(responseCode = "400", description = "Failed to save permission")
    public com.jimi.mybatisdemo1.common.ApiResponse<String> savePermission(@RequestBody Permission permission) {
        boolean result = permissionService.save(permission);
        if (result) {
            return com.jimi.mybatisdemo1.common.ApiResponse.success("Permission saved successfully");
        } else {
            return com.jimi.mybatisdemo1.common.ApiResponse.error("Failed to save permission");
        }
    }
    
    @PutMapping("/permissions")
    @Operation(summary = "Update a permission", description = "Update an existing permission in the system")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "Permission object to be updated", required = true, content = @Content(schema = @Schema(implementation = Permission.class)))
    @ApiResponse(responseCode = "200", description = "Permission updated successfully")
    @ApiResponse(responseCode = "400", description = "Failed to update permission")
    public com.jimi.mybatisdemo1.common.ApiResponse<String> updatePermission(@RequestBody Permission permission) {
        boolean result = permissionService.update(permission);
        if (result) {
            return com.jimi.mybatisdemo1.common.ApiResponse.success("Permission updated successfully");
        } else {
            return com.jimi.mybatisdemo1.common.ApiResponse.error("Failed to update permission");
        }
    }
    
    @DeleteMapping("/permissions/{id}")
    @Operation(summary = "Delete a permission", description = "Delete an existing permission by its ID")
    @Parameters({
        @Parameter(name = "id", in = ParameterIn.PATH, description = "ID of permission to be deleted", required = true, schema = @Schema(type = "integer", format = "int64"))
    })
    @ApiResponse(responseCode = "200", description = "Permission deleted successfully")
    @ApiResponse(responseCode = "404", description = "Failed to delete permission")
    public com.jimi.mybatisdemo1.common.ApiResponse<String> deletePermission(@PathVariable Long id) {
        boolean result = permissionService.deleteById(id);
        if (result) {
            return com.jimi.mybatisdemo1.common.ApiResponse.success("Permission deleted successfully");
        } else {
            return com.jimi.mybatisdemo1.common.ApiResponse.error("Failed to delete permission");
        }
    }
}