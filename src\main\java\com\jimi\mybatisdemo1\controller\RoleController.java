package com.jimi.mybatisdemo1.controller;

import com.jimi.mybatisdemo1.entity.Role;
import com.jimi.mybatisdemo1.service.RoleService;
import com.jimi.mybatisdemo1.common.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/roles")
public class RoleController {
    
    @Autowired
    private RoleService roleService;
    
    @GetMapping("/{id}")
    public ApiResponse<Role> findById(@PathVariable Long id) {
        Role role = roleService.findById(id);
        if (role != null) {
            return ApiResponse.success(role);
        } else {
            return ApiResponse.error(404, "Role not found");
        }
    }
    
    @GetMapping(params = "name")
    public ApiResponse<Role> findByName(@RequestParam String name) {
        Role role = roleService.findByName(name);
        if (role != null) {
            return ApiResponse.success(role);
        } else {
            return ApiResponse.error(404, "Role not found");
        }
    }
    
    @GetMapping
    public ApiResponse<List<Role>> findAll() {
        List<Role> roles = roleService.findAll();
        return ApiResponse.success(roles);
    }
    
    @PostMapping
    public ApiResponse<String> save(@RequestBody Role role) {
        boolean result = roleService.save(role);
        if (result) {
            return ApiResponse.success("Role saved successfully");
        } else {
            return ApiResponse.error("Failed to save role");
        }
    }
    
    @PutMapping
    public ApiResponse<String> update(@RequestBody Role role) {
        boolean result = roleService.update(role);
        if (result) {
            return ApiResponse.success("Role updated successfully");
        } else {
            return ApiResponse.error("Failed to update role");
        }
    }
    
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteById(@PathVariable Long id) {
        boolean result = roleService.deleteById(id);
        if (result) {
            return ApiResponse.success("Role deleted successfully");
        } else {
            return ApiResponse.error("Failed to delete role");
        }
    }
}