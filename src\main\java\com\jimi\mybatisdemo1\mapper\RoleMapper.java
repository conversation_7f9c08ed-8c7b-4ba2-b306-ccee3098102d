package com.jimi.mybatisdemo1.mapper;

import com.jimi.mybatisdemo1.entity.Role;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface RoleMapper {
    
    @Select("SELECT * FROM role WHERE id = #{id}")
    Role findById(Long id);
    
    @Select("SELECT * FROM role WHERE name = #{name}")
    Role findByName(String name);
    
    @Select("SELECT * FROM role")
    List<Role> findAll();
    
    @Insert("INSERT INTO role(name, description, create_time, update_time) VALUES(#{name}, #{description}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Role role);
    
    @Update("UPDATE role SET name=#{name}, description=#{description}, update_time=#{updateTime} WHERE id=#{id}")
    int update(Role role);
    
    @Delete("DELETE FROM role WHERE id = #{id}")
    int deleteById(Long id);
    
    Role findRoleWithPermissions(Long id);
}