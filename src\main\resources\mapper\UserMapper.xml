<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jimi.mybatisdemo1.mapper.UserMapper">
    
    <!-- Result Map for User -->
    <resultMap id="UserResultMap" type="com.jimi.mybatisdemo1.entity.User">
        <id property="id" column="id" />
        <result property="username" column="username" />
        <result property="password" column="password" />
        <result property="email" column="email" />
        <result property="role" column="role" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
    </resultMap>
    
    <!-- Find user with roles -->
    <select id="findUserWithRoles" resultMap="UserResultMap" flushCache="true">
        SELECT u.*, r.id as role_id, r.name as role_name, r.description as role_description
        FROM user u
        LEFT JOIN user_role ur ON u.id = ur.user_id
        LEFT JOIN role r ON ur.role_id = r.id
        WHERE u.id = #{id}
    </select>
    
</mapper>