package com.jimi.mybatisdemo1.mapper;

import com.jimi.mybatisdemo1.entity.User;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface UserMapper {
    
    @Select("SELECT * FROM user WHERE id = #{id}")
    @Options(flushCache = Options.FlushCachePolicy.TRUE)
    User findById(Long id);
    
    @Select("SELECT * FROM user WHERE username = #{username}")
    @Options(flushCache = Options.FlushCachePolicy.TRUE)
    User findByUsername(String username);
    
    @Select("SELECT * FROM user")
    @Options(flushCache = Options.FlushCachePolicy.TRUE)
    List<User> findAll();
    
    @Insert("INSERT INTO user(username, password, email, role, create_time, update_time) VALUES(#{username}, #{password}, #{email}, #{role}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id", flushCache = Options.FlushCachePolicy.TRUE)
    int insert(User user);
    
    @Update("UPDATE user SET username=#{username}, password=#{password}, email=#{email}, role=#{role}, update_time=#{updateTime} WHERE id=#{id}")
    @Options(flushCache = Options.FlushCachePolicy.TRUE)
    int update(User user);
    
    @Delete("DELETE FROM user WHERE id = #{id}")
    @Options(flushCache = Options.FlushCachePolicy.TRUE)
    int deleteById(Long id);
    
    User findUserWithRoles(Long id);
}