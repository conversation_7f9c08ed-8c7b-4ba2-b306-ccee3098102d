<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jimi.mybatisdemo1.mapper.PermissionMapper">
    
    <!-- Result Map for Permission -->
    <resultMap id="PermissionResultMap" type="com.jimi.mybatisdemo1.entity.Permission">
        <id property="id" column="id" />
        <result property="name" column="name" />
        <result property="description" column="description" />
        <result property="url" column="url" />
        <result property="method" column="method" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
    </resultMap>
    
    <!-- Find permissions by role id -->
    <select id="findByRoleId" resultMap="PermissionResultMap">
        SELECT p.*
        FROM permission p
        INNER JOIN role_permission rp ON p.id = rp.permission_id
        WHERE rp.role_id = #{roleId}
    </select>
    
</mapper>