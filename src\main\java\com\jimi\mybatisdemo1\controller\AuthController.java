package com.jimi.mybatisdemo1.controller;

import com.jimi.mybatisdemo1.common.ApiResponse;
import com.jimi.mybatisdemo1.dto.AuthRequest;
import com.jimi.mybatisdemo1.dto.AuthResponse;
import com.jimi.mybatisdemo1.entity.User;
import com.jimi.mybatisdemo1.service.UserService;
import com.jimi.mybatisdemo1.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/auth")
@Tag(name = "认证管理", description = "用户认证相关接口，包括登录和注册功能")
public class AuthController {
    
    @Autowired
    private AuthenticationManager authenticationManager;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @PostMapping("/login")
    @Operation(
        summary = "用户登录",
        description = "用户登录接口，成功后返回JWT token。请复制返回的token，点击页面右上角的'Authorize'按钮，输入'Bearer <token>'进行认证。",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "登录请求参数",
            required = true,
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = AuthRequest.class),
                examples = @ExampleObject(
                    name = "登录示例",
                    value = "{\n  \"username\": \"admin\",\n  \"password\": \"password\"\n}"
                )
            )
        )
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "登录成功",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = AuthResponse.class)
            )
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "401",
            description = "用户名或密码错误"
        )
    })
    public ApiResponse<AuthResponse> login(@Valid @RequestBody AuthRequest authRequest) {
        try {
            // Authenticate user
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            authRequest.getUsername(),
                            authRequest.getPassword()
                    )
            );
            
            SecurityContextHolder.getContext().setAuthentication(authentication);
            
            // Generate JWT token
            String token = jwtUtil.generateToken(authRequest.getUsername());
            
            return ApiResponse.success(new AuthResponse(token));
        } catch (Exception e) {
            return ApiResponse.error("Invalid username or password");
        }
    }
    
    @PostMapping("/register")
    @Operation(
        summary = "用户注册",
        description = "用户注册接口，创建新用户账户。默认角色为USER，管理员可以指定ADMIN角色。",
        requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "注册请求参数",
            required = true,
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = User.class),
                examples = @ExampleObject(
                    name = "注册示例",
                    value = "{\n  \"username\": \"newuser\",\n  \"password\": \"password123\",\n  \"email\": \"<EMAIL>\",\n  \"role\": \"USER\"\n}"
                )
            )
        )
    )
    @ApiResponses(value = {
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "200",
            description = "注册成功"
        ),
        @io.swagger.v3.oas.annotations.responses.ApiResponse(
            responseCode = "400",
            description = "用户名已存在或参数错误"
        )
    })
    public ApiResponse<String> register(@Valid @RequestBody User user) {
        try {
            // Check if user already exists
            User existingUser = userService.findByUsername(user.getUsername());
            if (existingUser != null) {
                return ApiResponse.error("Username already exists");
            }
            
            // Encode password
            user.setPassword(passwordEncoder.encode(user.getPassword()));
            
            // Set default role if not provided
            if (user.getRole() == null || user.getRole().isEmpty()) {
                user.setRole("USER");
            }
            
            // Save user
            boolean result = userService.save(user);
            if (result) {
                return ApiResponse.success("User registered successfully");
            } else {
                return ApiResponse.error("Failed to register user");
            }
        } catch (Exception e) {
            return ApiResponse.error("Failed to register user: " + e.getMessage());
        }
    }
}