package com.jimi.mybatisdemo1.controller;

import com.jimi.mybatisdemo1.common.ApiResponse;
import com.jimi.mybatisdemo1.dto.AuthRequest;
import com.jimi.mybatisdemo1.dto.AuthResponse;
import com.jimi.mybatisdemo1.entity.User;
import com.jimi.mybatisdemo1.service.UserService;
import com.jimi.mybatisdemo1.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/auth")
public class AuthController {
    
    @Autowired
    private AuthenticationManager authenticationManager;
    
    @Autowired
    private JwtUtil jwtUtil;
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private PasswordEncoder passwordEncoder;
    
    @PostMapping("/login")
    public ApiResponse<AuthResponse> login(@RequestBody AuthRequest authRequest) {
        try {
            // Authenticate user
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            authRequest.getUsername(),
                            authRequest.getPassword()
                    )
            );
            
            SecurityContextHolder.getContext().setAuthentication(authentication);
            
            // Generate JWT token
            String token = jwtUtil.generateToken(authRequest.getUsername());
            
            return ApiResponse.success(new AuthResponse(token));
        } catch (Exception e) {
            return ApiResponse.error("Invalid username or password");
        }
    }
    
    @PostMapping("/register")
    public ApiResponse<String> register(@RequestBody User user) {
        try {
            // Check if user already exists
            User existingUser = userService.findByUsername(user.getUsername());
            if (existingUser != null) {
                return ApiResponse.error("Username already exists");
            }
            
            // Encode password
            user.setPassword(passwordEncoder.encode(user.getPassword()));
            
            // Set default role if not provided
            if (user.getRole() == null || user.getRole().isEmpty()) {
                user.setRole("USER");
            }
            
            // Save user
            boolean result = userService.save(user);
            if (result) {
                return ApiResponse.success("User registered successfully");
            } else {
                return ApiResponse.error("Failed to register user");
            }
        } catch (Exception e) {
            return ApiResponse.error("Failed to register user: " + e.getMessage());
        }
    }
}