spring:
  application:
    name: mybatisdemo1

  # DataSource Configuration
  datasource:
    url: **************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver

  # MyBatis Configuration
  mybatis:
    configuration:
      map-underscore-to-camel-case: true
    mapper-locations: classpath:mapper/*.xml

  # Server Configuration
  server:
    port: 8080

  # Swagger Configuration
  springdoc:
    api-docs:
      path: /v3/api-docs
    swagger-ui:
      path: /swagger-ui.html

# 日志配置
logging:
  level:
    com.jimi.mybatisdemo1: DEBUG
    org.springframework: DEBUG
    org.mybatis: DEBUG
    com.zaxxer.hikari: DEBUG
  file:
    name: logs/app.log