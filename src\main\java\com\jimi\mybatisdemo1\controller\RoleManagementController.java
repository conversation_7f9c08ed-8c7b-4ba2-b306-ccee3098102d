package com.jimi.mybatisdemo1.controller;

import com.jimi.mybatisdemo1.entity.Role;
import com.jimi.mybatisdemo1.entity.RolePermission;
import com.jimi.mybatisdemo1.service.RoleService;
import com.jimi.mybatisdemo1.service.RolePermissionService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/role-management")
@Tag(name = "Role Management", description = "Operations pertaining to roles in the system")
public class RoleManagementController {
    
    @Autowired
    private RoleService roleService;
    
    @Autowired
    private RolePermissionService rolePermissionService;
    
    @GetMapping("/roles/{id}")
    @Operation(summary = "Find role by ID", description = "Get a role by its unique identifier")
    @Parameters({
        @Parameter(name = "id", in = ParameterIn.PATH, description = "ID of role to be searched", required = true, schema = @Schema(type = "integer", format = "int64"))
    })
    @ApiResponse(responseCode = "200", description = "Role found", content = @Content(schema = @Schema(implementation = Role.class)))
    @ApiResponse(responseCode = "404", description = "Role not found")
    public com.jimi.mybatisdemo1.common.ApiResponse<Role> findRoleById(@PathVariable Long id) {
        Role role = roleService.findById(id);
        if (role != null) {
            return com.jimi.mybatisdemo1.common.ApiResponse.success(role);
        } else {
            return com.jimi.mybatisdemo1.common.ApiResponse.error(404, "Role not found");
        }
    }
    
    @GetMapping("/roles")
    @Operation(summary = "Get all roles", description = "Returns a list of all roles in the system")
    @ApiResponse(responseCode = "200", description = "List of roles", content = @Content(schema = @Schema(implementation = Role.class)))
    public com.jimi.mybatisdemo1.common.ApiResponse<List<Role>> findAllRoles() {
        List<Role> roles = roleService.findAll();
        return com.jimi.mybatisdemo1.common.ApiResponse.success(roles);
    }
    
    @PostMapping("/roles")
    @Operation(summary = "Save a new role", description = "Create a new role in the system")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "Role object to be created", required = true, content = @Content(schema = @Schema(implementation = Role.class)))
    @ApiResponse(responseCode = "200", description = "Role saved successfully")
    @ApiResponse(responseCode = "400", description = "Failed to save role")
    public com.jimi.mybatisdemo1.common.ApiResponse<String> saveRole(@RequestBody Role role) {
        boolean result = roleService.save(role);
        if (result) {
            return com.jimi.mybatisdemo1.common.ApiResponse.success("Role saved successfully");
        } else {
            return com.jimi.mybatisdemo1.common.ApiResponse.error("Failed to save role");
        }
    }
    
    @PutMapping("/roles")
    @Operation(summary = "Update a role", description = "Update an existing role in the system")
    @io.swagger.v3.oas.annotations.parameters.RequestBody(description = "Role object to be updated", required = true, content = @Content(schema = @Schema(implementation = Role.class)))
    @ApiResponse(responseCode = "200", description = "Role updated successfully")
    @ApiResponse(responseCode = "400", description = "Failed to update role")
    public com.jimi.mybatisdemo1.common.ApiResponse<String> updateRole(@RequestBody Role role) {
        boolean result = roleService.update(role);
        if (result) {
            return com.jimi.mybatisdemo1.common.ApiResponse.success("Role updated successfully");
        } else {
            return com.jimi.mybatisdemo1.common.ApiResponse.error("Failed to update role");
        }
    }
    
    @DeleteMapping("/roles/{id}")
    @Operation(summary = "Delete a role", description = "Delete an existing role by its ID")
    @Parameters({
        @Parameter(name = "id", in = ParameterIn.PATH, description = "ID of role to be deleted", required = true, schema = @Schema(type = "integer", format = "int64"))
    })
    @ApiResponse(responseCode = "200", description = "Role deleted successfully")
    @ApiResponse(responseCode = "404", description = "Failed to delete role")
    public com.jimi.mybatisdemo1.common.ApiResponse<String> deleteRole(@PathVariable Long id) {
        boolean result = roleService.deleteById(id);
        if (result) {
            return com.jimi.mybatisdemo1.common.ApiResponse.success("Role deleted successfully");
        } else {
            return com.jimi.mybatisdemo1.common.ApiResponse.error("Failed to delete role");
        }
    }
    
    @PostMapping("/roles/{roleId}/permissions/{permissionId}")
    @Operation(summary = "Assign a permission to a role", description = "Assign a permission to an existing role")
    @Parameters({
        @Parameter(name = "roleId", in = ParameterIn.PATH, description = "ID of role to assign permission to", required = true, schema = @Schema(type = "integer", format = "int64")),
        @Parameter(name = "permissionId", in = ParameterIn.PATH, description = "ID of permission to assign", required = true, schema = @Schema(type = "integer", format = "int64"))
    })
    @ApiResponse(responseCode = "200", description = "Permission assigned to role successfully")
    @ApiResponse(responseCode = "400", description = "Failed to assign permission to role")
    public com.jimi.mybatisdemo1.common.ApiResponse<String> assignPermissionToRole(@PathVariable Long roleId, @PathVariable Long permissionId) {
        RolePermission rolePermission = new RolePermission();
        rolePermission.setRoleId(roleId);
        rolePermission.setPermissionId(permissionId);
        boolean result = rolePermissionService.save(rolePermission);
        if (result) {
            return com.jimi.mybatisdemo1.common.ApiResponse.success("Permission assigned to role successfully");
        } else {
            return com.jimi.mybatisdemo1.common.ApiResponse.error("Failed to assign permission to role");
        }
    }
    
    @DeleteMapping("/role-permission/{roleId}/{permissionId}")
    @Operation(summary = "Delete a role-permission association", description = "Remove a permission from a role")
    @Parameters({
        @Parameter(name = "roleId", in = ParameterIn.PATH, description = "ID of role to remove permission from", required = true, schema = @Schema(type = "integer", format = "int64")),
        @Parameter(name = "permissionId", in = ParameterIn.PATH, description = "ID of permission to remove", required = true, schema = @Schema(type = "integer", format = "int64"))
    })
    @ApiResponse(responseCode = "200", description = "Role-permission association deleted successfully")
    @ApiResponse(responseCode = "404", description = "Role-permission association not found")
    public com.jimi.mybatisdemo1.common.ApiResponse<String> deleteRolePermission(@PathVariable Long roleId, @PathVariable Long permissionId) {
        boolean result = rolePermissionService.deleteByRoleIdAndPermissionId(roleId, permissionId);
        if (result) {
            return com.jimi.mybatisdemo1.common.ApiResponse.success("Role-permission association deleted successfully");
        } else {
            return com.jimi.mybatisdemo1.common.ApiResponse.error(404, "Role-permission association not found");
        }
    }
}