<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jimi.mybatisdemo1.mapper.RoleMapper">
    
    <!-- Result Map for Role -->
    <resultMap id="RoleResultMap" type="com.jimi.mybatisdemo1.entity.Role">
        <id property="id" column="id" />
        <result property="name" column="name" />
        <result property="description" column="description" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
    </resultMap>
    
    <!-- Find role with permissions -->
    <select id="findRoleWithPermissions" resultMap="RoleResultMap">
        SELECT r.*, p.id as permission_id, p.name as permission_name, p.description as permission_description, p.url, p.method
        FROM role r
        LEFT JOIN role_permission rp ON r.id = rp.role_id
        LEFT JOIN permission p ON rp.permission_id = p.id
        WHERE r.id = #{id}
    </select>
    
</mapper>