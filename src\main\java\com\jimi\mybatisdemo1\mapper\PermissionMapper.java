package com.jimi.mybatisdemo1.mapper;

import com.jimi.mybatisdemo1.entity.Permission;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface PermissionMapper {
    
    @Select("SELECT * FROM permission WHERE id = #{id}")
    Permission findById(Long id);
    
    @Select("SELECT * FROM permission WHERE name = #{name}")
    Permission findByName(String name);
    
    @Select("SELECT * FROM permission")
    List<Permission> findAll();
    
    @Insert("INSERT INTO permission(name, description, url, method, create_time, update_time) VALUES(#{name}, #{description}, #{url}, #{method}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Permission permission);
    
    @Update("UPDATE permission SET name=#{name}, description=#{description}, url=#{url}, method=#{method}, update_time=#{updateTime} WHERE id=#{id}")
    int update(Permission permission);
    
    @Delete("DELETE FROM permission WHERE id = #{id}")
    int deleteById(Long id);
    
    List<Permission> findByRoleId(Long roleId);
}