package com.jimi.mybatisdemo1.service;

import com.jimi.mybatisdemo1.entity.Permission;

import java.util.List;

public interface PermissionService {
    Permission findById(Long id);
    Permission findByName(String name);
    List<Permission> findAll();
    boolean save(Permission permission);
    boolean update(Permission permission);
    boolean deleteById(Long id);
    boolean hasPermission(Long userId, String permission);
    boolean hasRole(Long userId, String roleName);
}