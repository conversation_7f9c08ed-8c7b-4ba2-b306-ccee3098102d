package com.jimi.mybatisdemo1.service.impl;

import com.jimi.mybatisdemo1.entity.User;
import com.jimi.mybatisdemo1.mapper.UserMapper;
import com.jimi.mybatisdemo1.service.UserService;
import com.jimi.mybatisdemo1.common.ResourceNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Date;
import java.util.List;

@Service
public class UserServiceImpl implements UserService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);
    
    @Autowired
    private UserMapper userMapper;
    
    @Override
    public User findById(Long id) {
        logger.debug("Finding user by id: {}", id);
        User user = userMapper.findById(id);
        if (user == null) {
            logger.debug("User not found with id: {}", id);
            throw new ResourceNotFoundException("User not found with id: " + id);
        }
        logger.debug("Found user: {}", user.getUsername());
        return user;
    }
    
    @Override
    public User findByUsername(String username) {
        logger.debug("Finding user by username: {}", username);
        User user = userMapper.findByUsername(username);
        if (user == null) {
            logger.debug("User not found with username: {}", username);
            throw new ResourceNotFoundException("User not found with username: " + username);
        }
        logger.debug("Found user: {}", user.getUsername());
        return user;
    }
    
    @Override
    public List<User> findAll() {
        logger.debug("Finding all users");
        List<User> users = userMapper.findAll();
        logger.debug("Found {} users", users.size());
        return users;
    }
    
    @Override
    public boolean save(User user) {
        logger.debug("Saving user: {}", user.getUsername());
        // Set default role if not provided
        if (user.getRole() == null || user.getRole().isEmpty()) {
            user.setRole("USER");
        }
        user.setCreateTime(new Date());
        user.setUpdateTime(new Date());
        int result = userMapper.insert(user);
        logger.debug("Insert result: {}", result);
        return result > 0;
    }
    
    @Override
    public boolean update(User user) {
        logger.debug("Updating user: {}", user.getUsername());
        // Check if user exists before updating
        User existingUser = userMapper.findById(user.getId());
        if (existingUser == null) {
            logger.debug("User not found with id: {}", user.getId());
            throw new ResourceNotFoundException("User not found with id: " + user.getId());
        }
        // Preserve existing role if not provided in update
        if (user.getRole() == null || user.getRole().isEmpty()) {
            user.setRole(existingUser.getRole());
        }
        user.setUpdateTime(new Date());
        int result = userMapper.update(user);
        logger.debug("Update result: {}", result);
        return result > 0;
    }
    
    @Override
    public boolean deleteById(Long id) {
        logger.debug("Deleting user by id: {}", id);
        // Check if user exists before deleting
        User existingUser = userMapper.findById(id);
        if (existingUser == null) {
            logger.debug("User not found with id: {}", id);
            throw new ResourceNotFoundException("User not found with id: " + id);
        }
        int result = userMapper.deleteById(id);
        logger.debug("Delete result: {}", result);
        return result > 0;
    }
}